# Facebook Automation Desktop

A powerful desktop application for Facebook automation with antidetect browser profiles, built with Electron and Python.

## Features

### 🔐 Profile Management
- Create and manage antidetect browser profiles
- Proxy support (HTTP, HTTPS, SOCKS5, SSH)
- Persistent Facebook login sessions
- Cookie management and storage

### 🔍 Facebook Scraping
- Automated post scraping from Facebook groups
- Extract user data (UID, name, gender, profile link)
- Configurable scraping options (comments, likes, shares)
- Export results to Excel

### 📨 Bulk Messaging
- Multi-threaded messaging system
- Import recipient lists
- Message templates and media support
- Anti-detection features
- Progress tracking and reporting

## Technology Stack

- **Frontend**: Electron + React + Ant Design
- **Backend**: Python + FastAPI + crawl4ai
- **Database**: SQLite + Redis
- **Browser Engine**: Playwright with antidetect capabilities

## Prerequisites

- Node.js 16+ and npm
- Python 3.8+
- Redis server (for caching and task queue)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd facebook-automation-desktop
   ```

2. **Install Node.js dependencies**
   ```bash
   npm install
   ```

3. **Setup Python environment**
   ```bash
   cd backend
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   
   pip install -r requirements.txt
   ```

4. **Install crawl4ai dependencies**
   ```bash
   cd crawl4ai
   pip install -e .
   ```

5. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

6. **Start Redis server**
   ```bash
   # On Windows (if using Redis for Windows)
   redis-server
   
   # On macOS (using Homebrew)
   brew services start redis
   
   # On Linux
   sudo systemctl start redis
   ```

## Development

1. **Start the development environment**
   ```bash
   npm run dev
   ```

   This will start:
   - Python backend server on http://localhost:8000
   - Electron app with hot reload

2. **Backend only**
   ```bash
   npm run dev:backend
   ```

3. **Frontend only**
   ```bash
   npm run dev:frontend
   ```

## Building

1. **Build for production**
   ```bash
   npm run build
   ```

2. **Package the application**
   ```bash
   npm run package
   ```

3. **Create distribution packages**
   ```bash
   npm run dist
   ```

## Project Structure

```
facebook-automation-desktop/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   └── services/       # Business logic
│   ├── main.py             # FastAPI app entry point
│   └── requirements.txt    # Python dependencies
├── src/
│   ├── main/               # Electron main process
│   │   ├── main.js         # Main process entry point
│   │   └── preload.js      # Preload script
│   └── renderer/           # Electron renderer process
│       ├── src/            # React application
│       │   ├── components/ # React components
│       │   ├── pages/      # Page components
│       │   ├── services/   # API services
│       │   └── styles/     # CSS styles
│       └── public/         # Static assets
├── crawl4ai/               # crawl4ai library
├── package.json            # Node.js dependencies
├── webpack.config.js       # Webpack configuration
└── README.md
```

## Usage

### Profile Management
1. Create a new profile with proxy settings
2. Test the profile connection
3. Login to Facebook and save session

### Facebook Scraping
1. Select a logged-in profile
2. Enter Facebook group/post URL
3. Configure scraping options
4. Start scraping and monitor progress
5. Export results to Excel

### Bulk Messaging
1. Import recipient list (Excel/CSV)
2. Select sender profiles
3. Configure messaging settings
4. Create message templates
5. Start bulk messaging campaign

## Configuration

Key configuration options in `.env`:

- `MAX_CONCURRENT_BROWSERS`: Maximum number of browser instances
- `MAX_SCRAPING_WORKERS`: Number of scraping workers
- `MAX_MESSAGING_WORKERS`: Number of messaging workers
- `SCRAPING_DELAY_MIN/MAX`: Delay between scraping actions
- `MESSAGE_DELAY_MIN/MAX`: Delay between messages

## Security & Anti-Detection

- Randomized user agents and fingerprints
- Human-like behavior patterns
- Configurable delays and rate limiting
- Proxy rotation support
- Session persistence

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is for educational and legitimate automation purposes only. Users are responsible for complying with Facebook's Terms of Service and applicable laws. The developers are not responsible for any misuse of this software.

## Support

For support and questions, please open an issue on GitHub.
