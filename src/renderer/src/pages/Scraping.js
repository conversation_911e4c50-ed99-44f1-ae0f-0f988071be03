/**
 * Scraping Page Component - Placeholder for Phase 2
 */

import React from 'react';
import { Card, Result, Button } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

const Scraping = () => {
  return (
    <div>
      <h1 style={{ marginBottom: 24 }}>Facebook Scraping</h1>
      
      <Card>
        <Result
          icon={<SearchOutlined style={{ color: '#1890ff' }} />}
          title="Scraping Module"
          subTitle="This module will be implemented in Phase 2"
          extra={
            <div>
              <p>Features coming soon:</p>
              <ul style={{ textAlign: 'left', display: 'inline-block' }}>
                <li>Automated post scraping from Facebook groups</li>
                <li>Extract user data (UID, name, gender, profile link)</li>
                <li>Configurable scraping options (comments, likes, shares)</li>
                <li>Export results to Excel</li>
                <li>Real-time progress tracking</li>
              </ul>
            </div>
          }
        />
      </Card>
    </div>
  );
};

export default Scraping;
