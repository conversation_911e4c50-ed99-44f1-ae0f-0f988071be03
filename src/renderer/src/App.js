/**
 * Facebook Automation Desktop - Main React App Component
 */

import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout, Menu, theme, Spin, notification } from 'antd';
import {
  UserOutlined,
  SearchOutlined,
  MessageOutlined,
  SettingOutlined,
  DashboardOutlined
} from '@ant-design/icons';

// Import page components
import Dashboard from './pages/Dashboard';
import ProfileManager from './pages/ProfileManager';
import Scraping from './pages/Scraping';
import Messaging from './pages/Messaging';
import Settings from './pages/Settings';

// Import services
import { apiService } from './services/api';

const { Header, Sider, Content } = Layout;

function App() {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const [backendStatus, setBackendStatus] = useState('connecting');
  const [selectedKey, setSelectedKey] = useState('dashboard');
  
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  // Menu items
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: 'profiles',
      icon: <UserOutlined />,
      label: 'Profile Manager',
    },
    {
      key: 'scraping',
      icon: <SearchOutlined />,
      label: 'Facebook Scraping',
    },
    {
      key: 'messaging',
      icon: <MessageOutlined />,
      label: 'Bulk Messaging',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  // Check backend status on component mount
  useEffect(() => {
    checkBackendStatus();
  }, []);

  const checkBackendStatus = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/health');
      
      if (response.status === 'healthy') {
        setBackendStatus('connected');
        notification.success({
          message: 'Backend Connected',
          description: 'Successfully connected to the backend server.',
          duration: 3,
        });
      }
    } catch (error) {
      console.error('Backend connection failed:', error);
      setBackendStatus('error');
      notification.error({
        message: 'Backend Connection Failed',
        description: 'Could not connect to the backend server. Please check if it\'s running.',
        duration: 5,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = ({ key }) => {
    setSelectedKey(key);
  };

  const renderContent = () => {
    switch (selectedKey) {
      case 'dashboard':
        return <Dashboard />;
      case 'profiles':
        return <ProfileManager />;
      case 'scraping':
        return <Scraping />;
      case 'messaging':
        return <Messaging />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard />;
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column'
      }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>Connecting to backend...</p>
      </div>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="dark"
      >
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'FB' : 'Facebook Auto'}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedKey]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          padding: 0, 
          background: colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingRight: 24
        }}>
          <div style={{ paddingLeft: 24, fontSize: 18, fontWeight: 'bold' }}>
            Facebook Automation Desktop
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <div style={{ 
              padding: '4px 12px', 
              borderRadius: 4,
              backgroundColor: backendStatus === 'connected' ? '#52c41a' : '#ff4d4f',
              color: 'white',
              fontSize: 12
            }}>
              {backendStatus === 'connected' ? 'Backend Online' : 'Backend Offline'}
            </div>
          </div>
        </Header>
        
        <Content style={{ 
          margin: '24px 16px',
          padding: 24,
          minHeight: 280,
          background: colorBgContainer,
          borderRadius: 6
        }}>
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
