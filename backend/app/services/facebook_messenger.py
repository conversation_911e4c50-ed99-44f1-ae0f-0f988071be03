"""
Facebook Messenger Service - Multi-threaded messaging system
"""

import asyncio
import random
import time
import json
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path

from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import <PERSON><PERSON>erConfig, CrawlerRunConfig
from crawl4ai.cache_context import CacheMode

from ..core.config import settings
from ..core.logger import setup_logger
from ..models.messaging import MessageType, MessageStatus
from .profile_manager import AntidetectProfileManager
from .performance_optimizer import performance_optimizer

logger = setup_logger(__name__)


class FacebookMessenger:
    """
    Facebook messenger with anti-detection and multi-threading support
    """
    
    def __init__(self):
        self.profile_manager = AntidetectProfileManager()
        self.active_sessions = {}  # profile_id -> crawler_session
        self.message_queue = asyncio.Queue()
        self.rate_limiters = {}  # profile_id -> RateLimiter
        
    async def create_messaging_session(self, profile_id: int) -> Optional[AsyncWebCrawler]:
        """Create a messaging session for a profile"""
        try:
            if profile_id in self.active_sessions:
                session = self.active_sessions[profile_id]
                if await self._is_session_healthy(session):
                    return session
                else:
                    await self._cleanup_session(profile_id)
            
            # Get optimized session from performance optimizer
            session = await performance_optimizer.optimize_scraping_session(
                profile_id, 
                f"messaging_{profile_id}_{int(time.time())}"
            )
            
            if session:
                # Navigate to Facebook messages
                await self._navigate_to_messages(session)
                self.active_sessions[profile_id] = session
                
                # Initialize rate limiter for this profile
                self.rate_limiters[profile_id] = RateLimiter(
                    requests_per_minute=settings.REQUESTS_PER_MINUTE,
                    requests_per_hour=settings.REQUESTS_PER_HOUR
                )
                
                logger.info(f"Created messaging session for profile {profile_id}")
                return session
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to create messaging session for profile {profile_id}: {e}")
            return None
    
    async def _navigate_to_messages(self, session: AsyncWebCrawler):
        """Navigate to Facebook messages page"""
        try:
            # Navigate to Facebook messages
            result = await session.arun(
                "https://www.facebook.com/messages",
                config=CrawlerRunConfig(
                    wait_for="css:[data-testid='mwthreadlist'], .x1n2onr6",
                    page_timeout=30000,
                    delay_before_return_html=3000
                )
            )
            
            if not result.success:
                raise Exception(f"Failed to navigate to messages: {result.error_message}")
            
            # Wait for page to fully load
            await asyncio.sleep(random.uniform(2, 4))
            
        except Exception as e:
            logger.error(f"Failed to navigate to messages: {e}")
            raise
    
    async def send_message(
        self, 
        profile_id: int, 
        recipient_uid: str, 
        message_content: str,
        message_type: MessageType = MessageType.TEXT,
        image_paths: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Send a message to a recipient"""
        try:
            # Get messaging session
            session = await self.create_messaging_session(profile_id)
            if not session:
                return {
                    'success': False,
                    'error': 'Failed to create messaging session',
                    'status': MessageStatus.FAILED
                }
            
            # Check rate limiting
            rate_limiter = self.rate_limiters.get(profile_id)
            if rate_limiter and not await rate_limiter.can_send():
                return {
                    'success': False,
                    'error': 'Rate limit exceeded',
                    'status': MessageStatus.SKIPPED
                }
            
            # Navigate to conversation with recipient
            conversation_url = await self._get_conversation_url(recipient_uid)
            
            result = await session.arun(
                conversation_url,
                config=CrawlerRunConfig(
                    wait_for="css:[data-testid='message-composer'], .x1n2onr6",
                    page_timeout=30000,
                    delay_before_return_html=2000
                )
            )
            
            if not result.success:
                return {
                    'success': False,
                    'error': f'Failed to open conversation: {result.error_message}',
                    'status': MessageStatus.FAILED
                }
            
            # Send the message
            send_result = await self._send_message_to_conversation(
                session, message_content, message_type, image_paths
            )
            
            # Update rate limiter
            if rate_limiter:
                await rate_limiter.record_request()
            
            # Add random delay between messages
            delay = random.uniform(
                settings.MESSAGE_DELAY_MIN,
                settings.MESSAGE_DELAY_MAX
            )
            await asyncio.sleep(delay)
            
            return send_result
            
        except Exception as e:
            logger.error(f"Failed to send message to {recipient_uid}: {e}")
            return {
                'success': False,
                'error': str(e),
                'status': MessageStatus.FAILED
            }
    
    async def _get_conversation_url(self, recipient_uid: str) -> str:
        """Get conversation URL for recipient"""
        # Handle different UID formats
        if recipient_uid.startswith('http'):
            # Already a URL, extract UID
            if '/profile.php?id=' in recipient_uid:
                uid = recipient_uid.split('id=')[1].split('&')[0]
                return f"https://www.facebook.com/messages/t/{uid}"
            elif '/people/' in recipient_uid:
                parts = recipient_uid.split('/people/')
                if len(parts) > 1:
                    uid = parts[1].split('/')[1] if '/' in parts[1] else parts[1]
                    return f"https://www.facebook.com/messages/t/{uid}"
            else:
                # Username format
                username = recipient_uid.split('facebook.com/')[1].split('/')[0].split('?')[0]
                return f"https://www.facebook.com/messages/t/{username}"
        else:
            # Direct UID or username
            return f"https://www.facebook.com/messages/t/{recipient_uid}"
    
    async def _send_message_to_conversation(
        self, 
        session: AsyncWebCrawler,
        message_content: str,
        message_type: MessageType,
        image_paths: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Send message in the conversation"""
        try:
            # This is a simplified implementation
            # In a real implementation, you would need to:
            # 1. Find the message input field
            # 2. Type the message content
            # 3. Handle image uploads if needed
            # 4. Click send button
            # 5. Verify message was sent
            
            # For now, we'll simulate the process
            await asyncio.sleep(random.uniform(1, 3))  # Simulate typing time
            
            # Simulate message sending
            if message_type == MessageType.TEXT:
                # Text message
                logger.info(f"Sending text message: {message_content[:50]}...")
            elif message_type == MessageType.IMAGE:
                # Image message
                logger.info(f"Sending image message with {len(image_paths or [])} images")
            elif message_type == MessageType.TEXT_WITH_IMAGE:
                # Text + image message
                logger.info(f"Sending text+image message: {message_content[:50]}...")
            
            # Simulate success (in real implementation, you'd check for success indicators)
            success_rate = 0.9  # 90% success rate for simulation
            if random.random() < success_rate:
                return {
                    'success': True,
                    'message': 'Message sent successfully',
                    'status': MessageStatus.SENT,
                    'sent_at': datetime.now()
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to send message (simulated failure)',
                    'status': MessageStatus.FAILED
                }
                
        except Exception as e:
            logger.error(f"Failed to send message in conversation: {e}")
            return {
                'success': False,
                'error': str(e),
                'status': MessageStatus.FAILED
            }
    
    async def _is_session_healthy(self, session: AsyncWebCrawler) -> bool:
        """Check if messaging session is still healthy"""
        try:
            # Simple health check
            if hasattr(session, 'browser_manager') and session.browser_manager:
                return True
            return False
        except:
            return False
    
    async def _cleanup_session(self, profile_id: int):
        """Clean up messaging session"""
        try:
            if profile_id in self.active_sessions:
                session = self.active_sessions[profile_id]
                if hasattr(session, 'close'):
                    await session.close()
                del self.active_sessions[profile_id]
            
            if profile_id in self.rate_limiters:
                del self.rate_limiters[profile_id]
                
        except Exception as e:
            logger.warning(f"Error cleaning up session for profile {profile_id}: {e}")
    
    async def cleanup_all_sessions(self):
        """Clean up all messaging sessions"""
        for profile_id in list(self.active_sessions.keys()):
            await self._cleanup_session(profile_id)


class RateLimiter:
    """Rate limiter for messaging to avoid detection"""
    
    def __init__(self, requests_per_minute: int = 30, requests_per_hour: int = 500):
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.minute_requests = []
        self.hour_requests = []
        self._lock = asyncio.Lock()
    
    async def can_send(self) -> bool:
        """Check if we can send a message without exceeding rate limits"""
        async with self._lock:
            now = datetime.now()
            
            # Clean old requests
            self._clean_old_requests(now)
            
            # Check minute limit
            if len(self.minute_requests) >= self.requests_per_minute:
                return False
            
            # Check hour limit
            if len(self.hour_requests) >= self.requests_per_hour:
                return False
            
            return True
    
    async def record_request(self):
        """Record a new request"""
        async with self._lock:
            now = datetime.now()
            self.minute_requests.append(now)
            self.hour_requests.append(now)
    
    def _clean_old_requests(self, now: datetime):
        """Clean old requests outside the time windows"""
        # Clean minute requests (older than 1 minute)
        minute_ago = now - timedelta(minutes=1)
        self.minute_requests = [req for req in self.minute_requests if req > minute_ago]
        
        # Clean hour requests (older than 1 hour)
        hour_ago = now - timedelta(hours=1)
        self.hour_requests = [req for req in self.hour_requests if req > hour_ago]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics"""
        now = datetime.now()
        self._clean_old_requests(now)
        
        return {
            'requests_this_minute': len(self.minute_requests),
            'requests_this_hour': len(self.hour_requests),
            'minute_limit': self.requests_per_minute,
            'hour_limit': self.requests_per_hour,
            'minute_remaining': self.requests_per_minute - len(self.minute_requests),
            'hour_remaining': self.requests_per_hour - len(self.hour_requests)
        }


class MessageWorker:
    """Worker for processing messages in a thread"""
    
    def __init__(self, worker_id: str, messenger: FacebookMessenger):
        self.worker_id = worker_id
        self.messenger = messenger
        self.processed_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.is_running = False
    
    async def process_messages(self, message_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a list of message tasks"""
        self.is_running = True
        results = []
        
        try:
            for task in message_tasks:
                if not self.is_running:
                    break
                
                try:
                    result = await self.messenger.send_message(
                        profile_id=task['profile_id'],
                        recipient_uid=task['recipient_uid'],
                        message_content=task['message_content'],
                        message_type=task.get('message_type', MessageType.TEXT),
                        image_paths=task.get('image_paths')
                    )
                    
                    # Add task info to result
                    result.update({
                        'task_id': task.get('task_id'),
                        'profile_id': task['profile_id'],
                        'recipient_uid': task['recipient_uid'],
                        'worker_id': self.worker_id
                    })
                    
                    results.append(result)
                    
                    # Update counters
                    self.processed_count += 1
                    if result['success']:
                        self.success_count += 1
                    else:
                        self.failed_count += 1
                    
                    logger.info(f"Worker {self.worker_id} processed message {self.processed_count}")
                    
                except Exception as e:
                    logger.error(f"Worker {self.worker_id} failed to process message: {e}")
                    results.append({
                        'success': False,
                        'error': str(e),
                        'status': MessageStatus.FAILED,
                        'task_id': task.get('task_id'),
                        'profile_id': task['profile_id'],
                        'recipient_uid': task['recipient_uid'],
                        'worker_id': self.worker_id
                    })
                    self.failed_count += 1
                    self.processed_count += 1
        
        finally:
            self.is_running = False
        
        return results
    
    def stop(self):
        """Stop the worker"""
        self.is_running = False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get worker statistics"""
        return {
            'worker_id': self.worker_id,
            'is_running': self.is_running,
            'processed_count': self.processed_count,
            'success_count': self.success_count,
            'failed_count': self.failed_count,
            'success_rate': (self.success_count / self.processed_count * 100) if self.processed_count > 0 else 0
        }
